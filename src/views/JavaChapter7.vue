<template>
  <div class="java-chapter7">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <h1 class="chapter-title">第七章：理解 Java 性能 (Understanding Java Performance)</h1>
          <p class="chapter-subtitle">从度量到优化：科学的性能分析方法论</p>
          <div class="chapter-badge">
            <span class="badge-text">Performance Tuning</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <span class="progress-text">{{ progress }}% 完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 性能优化的核心理念 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="性能优化的核心理念 (Core Philosophy of Performance Tuning)"
                :concept-data="performancePhilosophyData"
                @interaction="handleInteraction"
              >
                <div class="performance-philosophy-showcase">
                  <h3>🎯 性能优化的核心理念</h3>

                  <div class="philosophy-intro">
                    <div class="concept-definition">
                      <h4>💡 概念定义</h4>
                      <div class="definition-content">
                        <p>
                          Java 性能优化是一门<strong>实验科学</strong>，其核心是
                          <strong
                            >通过精确的度量来识别和解决系统瓶颈，从而提升关键性能指标的过程</strong
                          >。 它不是凭感觉、靠"技巧"的艺术创作。
                        </p>
                        <div class="human-explanation">
                          <h5>人话版解释</h5>
                          <p>
                            别再猜了！你的代码哪里慢，不是你觉得慢，而是测量工具告诉你它慢。
                            性能优化的第一步，永远是拿起"秒表和尺子"（性能分析工具），
                            而不是"锤子和钉子"（代码修改）。
                          </p>
                        </div>
                      </div>
                    </div>

                    <div class="core-principles">
                      <h4>🔬 核心原理</h4>
                      <div class="principles-grid">
                        <div class="principle-card">
                          <div class="principle-icon">📊</div>
                          <h5>度量驱动</h5>
                          <p>
                            "你无法优化你无法度量的东西"。人类的大脑和直觉在判断复杂的软件系统瓶颈时，几乎总是出错。
                          </p>
                        </div>
                        <div class="principle-card">
                          <div class="principle-icon">🎯</div>
                          <h5>宏观视角</h5>
                          <p>
                            性能优化应该关注宏观的用户体验指标（如99%响应时间），而不是孤立的微观指标。
                          </p>
                        </div>
                        <div class="principle-card">
                          <div class="principle-icon">🔍</div>
                          <h5>瓶颈识别</h5>
                          <p>
                            开发者常常会陷入对算法细节的"微优化"，而真正的瓶颈可能在数据库查询、网络I/O、甚至是GC暂停上。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="analogy-section">
                    <h4>🏥 生动类比：性能优化就像医生看病</h4>
                    <div class="analogy-comparison">
                      <div class="comparison-item wrong">
                        <div class="comparison-header">
                          <span class="comparison-icon">❌</span>
                          <h5>错误方式（猜）</h5>
                        </div>
                        <div class="comparison-content">
                          <p>病人说头疼，你就给他开头疼药。</p>
                          <div class="example">
                            <strong>开发中的体现：</strong>
                            <p>用户反馈系统慢，就直接优化循环算法。</p>
                          </div>
                        </div>
                      </div>

                      <div class="comparison-arrow">→</div>

                      <div class="comparison-item correct">
                        <div class="comparison-header">
                          <span class="comparison-icon">✅</span>
                          <h5>正确方式（度量）</h5>
                        </div>
                        <div class="comparison-content">
                          <p>
                            用听诊器、血压计、CT机（性能分析工具）做一整套检查（度量），找到病根（瓶颈）。
                          </p>
                          <div class="example">
                            <strong>开发中的体现：</strong>
                            <p>使用APM工具发现90%时间消耗在RPC调用上，最终只需加个数据库索引。</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>常见问题：团队陷入"猜测式优化"泥潭</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              项目上线后，用户反馈系统"很卡"。团队成员A认为是排序算法效率低，B认为是JSON序列化慢，
                              C认为是日志打印太多。大家在没有数据支撑的情况下，各自为战，修改了多个地方，
                              发布新版本后，问题依旧，甚至引入了新Bug。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              <strong>缺乏统一的、数据驱动的分析流程</strong>。
                              团队陷入了"猜测式优化"的泥潭，浪费了大量开发时间，却没找到真正的性能瓶颈。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案：S.M.A.R.T. 方法</h6>
                            <div class="smart-approach">
                              <div class="smart-step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                  <h7>定义目标 (Specific & Measurable)</h7>
                                  <p>
                                    明确优化的目标且必须可度量。例如："将用户下单接口的99分位响应时间从2秒降低到500毫秒"
                                  </p>
                                </div>
                              </div>
                              <div class="smart-step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                  <h7>建立基线 (Agreed & Relevant)</h7>
                                  <p>
                                    使用自动化性能测试工具和监控工具对现有系统进行完整测试，记录性能基线数据
                                  </p>
                                </div>
                              </div>
                              <div class="smart-step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                  <h7>分析瓶颈 (Profiling)</h7>
                                  <p>使用性能剖析工具找到消耗CPU或内存最多的热点代码路径</p>
                                </div>
                              </div>
                              <div class="smart-step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                  <h7>单点优化 (Time-boxed)</h7>
                                  <p>根据分析结果，只修改一个被证明是瓶颈的地方</p>
                                </div>
                              </div>
                              <div class="smart-step">
                                <div class="step-number">5</div>
                                <div class="step-content">
                                  <h7>回归测试</h7>
                                  <p>再次运行同样的性能测试，将结果与基线进行对比，验证优化效果</p>
                                </div>
                              </div>
                              <div class="smart-step">
                                <div class="step-number">6</div>
                                <div class="step-content">
                                  <h7>循环迭代</h7>
                                  <p>如果未达到目标，重复步骤3-5。如果已达到，则停止优化</p>
                                </div>
                              </div>
                            </div>

                            <div class="trade-offs">
                              <h6>权衡分析</h6>
                              <div class="pros-cons">
                                <div class="pros">
                                  <h7>优点</h7>
                                  <ul>
                                    <li>流程科学、目标明确、结果可量化</li>
                                    <li>避免了盲目修改和团队内耗</li>
                                    <li>每一步的改动都有数据支撑</li>
                                  </ul>
                                </div>
                                <div class="cons">
                                  <h7>缺点</h7>
                                  <ul>
                                    <li>前期需要投入时间搭建监控和测试环境</li>
                                    <li>对团队成员的能力有一定要求</li>
                                  </ul>
                                </div>
                              </div>
                              <div class="conclusion">
                                <strong>业界选择：</strong>
                                这是所有专业技术团队进行性能优化时遵循的标准流程。
                                <strong>没有度量，就没有优化</strong>，已经成为业界共识。
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="common-pitfalls">
                    <h4>⚠️ 常见理论误区</h4>
                    <div class="pitfalls-grid">
                      <div class="pitfall-card">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">🚫</span>
                          <h5>误区1：过早优化是万恶之源</h5>
                        </div>
                        <div class="pitfall-content">
                          <p>
                            这是对 Donald Knuth
                            名言的滥用。原文强调的是不要在不确定瓶颈的情况下优化"非关键部分"。
                            而一些好的编程习惯，如避免不必要的对象创建，本身就是高质量代码的一部分。
                          </p>
                        </div>
                      </div>
                      <div class="pitfall-card">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">📚</span>
                          <h5>误区2：追求所谓的"性能技巧大全"</h5>
                        </div>
                        <div class="pitfall-content">
                          <p>
                            JVM越来越智能，很多旧的"技巧"在现代JVM上已经失效甚至有害。
                            性能知识需要与时俱进，理解原理比背诵技巧更重要。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 性能度量关键术语 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="性能度量关键术语 (Key Performance Terminology)"
                :concept-data="performanceTerminologyData"
                @interaction="handleInteraction"
              >
                <div class="performance-terminology-showcase">
                  <h3>📊 性能度量关键术语</h3>

                  <div class="terminology-intro">
                    <p>
                      这是性能工程领域的"通用语言"，用于精确描述和衡量系统的性能表现。
                      提供了一个共同的、无歧义的框架来讨论和评估性能。
                    </p>
                  </div>

                  <div class="key-metrics">
                    <h4>🎯 核心性能指标</h4>
                    <div class="metrics-grid">
                      <div class="metric-card latency">
                        <div class="metric-icon">⏱️</div>
                        <h5>延迟 (Latency)</h5>
                        <div class="metric-definition">
                          <p><strong>定义：</strong>处理单个请求所需的时间</p>
                          <p><strong>示例：</strong>一次API调用耗时50毫秒</p>
                        </div>
                        <div class="metric-details">
                          <h6>关键细节</h6>
                          <ul>
                            <li>通常关注P99延迟（99%的请求响应时间）</li>
                            <li>比平均延迟更能反映用户体验</li>
                            <li>长尾延迟往往暴露系统瓶颈</li>
                          </ul>
                        </div>
                      </div>

                      <div class="metric-card throughput">
                        <div class="metric-icon">🚀</div>
                        <h5>吞吐量 (Throughput)</h5>
                        <div class="metric-definition">
                          <p><strong>定义：</strong>单位时间内系统能处理的请求数量</p>
                          <p><strong>示例：</strong>每秒处理1000个请求 (QPS)</p>
                        </div>
                        <div class="metric-details">
                          <h6>关键细节</h6>
                          <ul>
                            <li>衡量系统的处理能力</li>
                            <li>与延迟通常存在权衡关系</li>
                            <li>受限于系统的最慢组件</li>
                          </ul>
                        </div>
                      </div>

                      <div class="metric-card utilization">
                        <div class="metric-icon">📈</div>
                        <h5>利用率 (Utilization)</h5>
                        <div class="metric-definition">
                          <p><strong>定义：</strong>资源被使用的时间百分比</p>
                          <p><strong>示例：</strong>CPU利用率80%</p>
                        </div>
                        <div class="metric-details">
                          <h6>关键细节</h6>
                          <ul>
                            <li>反映资源使用效率</li>
                            <li>过高可能导致性能衰减</li>
                            <li>需要考虑峰值和平均值</li>
                          </ul>
                        </div>
                      </div>

                      <div class="metric-card scalability">
                        <div class="metric-icon">📊</div>
                        <h5>可伸缩性 (Scalability)</h5>
                        <div class="metric-definition">
                          <p><strong>定义：</strong>当增加资源时，吞吐量提升的能力</p>
                          <p><strong>示例：</strong>CPU翻倍，吞吐量提升80%</p>
                        </div>
                        <div class="metric-details">
                          <h6>关键细节</h6>
                          <ul>
                            <li>线性伸缩是理想状态</li>
                            <li>受限于系统架构设计</li>
                            <li>存在伸缩性上限</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="littles-law-section">
                    <h4>⚖️ 利特尔法则 (Little's Law)</h4>
                    <div class="law-explanation">
                      <div class="law-formula">
                        <div class="formula-box">
                          <h5>核心公式</h5>
                          <div class="formula-display">
                            <span class="formula-text"
                              >系统中的平均请求数 = 请求到达率 × 平均请求处理时间</span
                            >
                          </div>
                          <div class="formula-variables">
                            <div class="variable">
                              <span class="var-symbol">L</span>
                              <span class="var-desc">= 系统中的平均请求数</span>
                            </div>
                            <div class="variable">
                              <span class="var-symbol">λ</span>
                              <span class="var-desc">= 请求到达率 (requests/second)</span>
                            </div>
                            <div class="variable">
                              <span class="var-symbol">W</span>
                              <span class="var-desc">= 平均请求处理时间 (seconds)</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="law-implications">
                        <h5>实际意义</h5>
                        <div class="implications-grid">
                          <div class="implication-card">
                            <div class="implication-icon">📈</div>
                            <h6>容量规划</h6>
                            <p>帮助预测系统在不同负载下的行为，指导资源配置</p>
                          </div>
                          <div class="implication-card">
                            <div class="implication-icon">⚡</div>
                            <h6>性能优化</h6>
                            <p>揭示延迟和吞吐量的内在关系，指导优化方向</p>
                          </div>
                          <div class="implication-card">
                            <div class="implication-icon">🎯</div>
                            <h6>瓶颈识别</h6>
                            <p>当实际值偏离理论值时，说明存在系统瓶颈</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 硬件挑战与瓶颈 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="性能瓶颈的根源：硬件的挑战 (Root of Bottlenecks: The Hardware Challenge)"
                :concept-data="hardwareChallengeData"
                @interaction="handleInteraction"
              >
                <div class="hardware-challenge-showcase">
                  <h3>🖥️ 性能瓶颈的根源：硬件的挑战</h3>

                  <div class="challenge-intro">
                    <div class="concept-definition">
                      <h4>💡 概念定义</h4>
                      <div class="definition-content">
                        <p>
                          现代 Java 性能问题的核心，已不再是语言本身，而是 Java
                          程序在现代计算机硬件上运行时所面临的物理限制，
                          主要是<strong>处理器速度与内存访问速度之间的巨大鸿沟</strong>。
                        </p>
                        <div class="human-explanation">
                          <h5>人话版解释</h5>
                          <p>
                            你的CPU运算速度像一辆法拉利，而你的内存（RAM）就像一条乡间土路。
                            法拉利大部分时间都在等待土路上的"数据"运过来，而不是在赛道上飞驰。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="memory-hierarchy">
                    <h4>🏗️ 内存层级结构 (Memory Hierarchy)</h4>
                    <div class="hierarchy-visualization">
                      <div class="hierarchy-level cpu">
                        <div class="level-info">
                          <h5>CPU寄存器</h5>
                          <div class="level-stats">
                            <span class="latency">~0.3ns</span>
                            <span class="size">~1KB</span>
                          </div>
                        </div>
                        <div class="level-description">
                          <p>最快但容量最小，CPU直接访问</p>
                        </div>
                      </div>

                      <div class="hierarchy-arrow">↓</div>

                      <div class="hierarchy-level l1">
                        <div class="level-info">
                          <h5>L1 Cache</h5>
                          <div class="level-stats">
                            <span class="latency">~1ns</span>
                            <span class="size">~32KB</span>
                          </div>
                        </div>
                        <div class="level-description">
                          <p>CPU核心专用缓存，分为指令和数据缓存</p>
                        </div>
                      </div>

                      <div class="hierarchy-arrow">↓</div>

                      <div class="hierarchy-level l2">
                        <div class="level-info">
                          <h5>L2 Cache</h5>
                          <div class="level-stats">
                            <span class="latency">~3-5ns</span>
                            <span class="size">~256KB</span>
                          </div>
                        </div>
                        <div class="level-description">
                          <p>CPU核心专用，容量更大但速度稍慢</p>
                        </div>
                      </div>

                      <div class="hierarchy-arrow">↓</div>

                      <div class="hierarchy-level l3">
                        <div class="level-info">
                          <h5>L3 Cache</h5>
                          <div class="level-stats">
                            <span class="latency">~10-20ns</span>
                            <span class="size">~8-32MB</span>
                          </div>
                        </div>
                        <div class="level-description">
                          <p>多核共享缓存，最后一级缓存</p>
                        </div>
                      </div>

                      <div class="hierarchy-arrow">↓</div>

                      <div class="hierarchy-level ram">
                        <div class="level-info">
                          <h5>主存 (RAM)</h5>
                          <div class="level-stats">
                            <span class="latency">~50-100ns</span>
                            <span class="size">~8-64GB</span>
                          </div>
                        </div>
                        <div class="level-description">
                          <p>系统主内存，容量大但访问延迟高</p>
                        </div>
                      </div>

                      <div class="hierarchy-arrow">↓</div>

                      <div class="hierarchy-level disk">
                        <div class="level-info">
                          <h5>磁盘存储</h5>
                          <div class="level-stats">
                            <span class="latency">~1-10ms</span>
                            <span class="size">~1TB+</span>
                          </div>
                        </div>
                        <div class="level-description">
                          <p>持久化存储，延迟极高但容量巨大</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="cache-miss-section">
                    <h4>💥 缓存未命中：性能杀手</h4>
                    <div class="cache-miss-explanation">
                      <div class="miss-analogy">
                        <h5>🍽️ 生动类比：你是一位大厨（CPU）</h5>
                        <div class="chef-analogy">
                          <div class="analogy-level">
                            <div class="level-icon">🥄</div>
                            <div class="level-content">
                              <h6>L1 Cache - 手边的小碗</h6>
                              <p>装着葱姜蒜，伸手就能拿到</p>
                            </div>
                          </div>
                          <div class="analogy-level">
                            <div class="level-icon">🍳</div>
                            <div class="level-content">
                              <h6>L2/L3 Cache - 身后的操作台</h6>
                              <p>放着切好的蔬菜和肉，转身就能取到</p>
                            </div>
                          </div>
                          <div class="analogy-level">
                            <div class="level-icon">🧊</div>
                            <div class="level-content">
                              <h6>主存 (RAM) - 厨房的大冰箱</h6>
                              <p>需要走几步去翻找，期间你只能等待</p>
                            </div>
                          </div>
                          <div class="analogy-level">
                            <div class="level-icon">🏪</div>
                            <div class="level-content">
                              <h6>硬盘 - 超市的仓库</h6>
                              <p>需要开车去采购，耗时极长</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="locality-principles">
                        <h5>📍 数据局部性原理</h5>
                        <div class="locality-grid">
                          <div class="locality-card temporal">
                            <div class="locality-icon">⏰</div>
                            <h6>时间局部性 (Temporal Locality)</h6>
                            <p>一个数据被访问后，很可能在短时间内再次被访问</p>
                            <div class="locality-example">
                              <strong>例子：</strong>循环中的计数器变量
                            </div>
                          </div>
                          <div class="locality-card spatial">
                            <div class="locality-icon">📍</div>
                            <h6>空间局部性 (Spatial Locality)</h6>
                            <p>一个数据被访问后，其物理上相邻的数据也很可能被访问</p>
                            <div class="locality-example">
                              <strong>例子：</strong>数组的顺序遍历
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions for Hardware Challenge -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>常见问题：伪共享导致的性能衰减</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              一个金融计算服务，在处理一个巨大的浮点数数组时，代码逻辑非常简单，
                              就是对每个元素进行一个乘法运算。但性能始终不达标，即使增加了CPU核心数，提升也微乎其微。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              <strong>伪共享 (False Sharing)</strong
                              >。在多核CPU中，缓存是以缓存行（Cache
                              Line，通常是64字节）为单位进行同步的。
                              如果两个线程需要频繁更新的两个独立变量恰好位于同一个缓存行中，那么当线程A修改变量a时，
                              会导致整个缓存行失效。此时，线程B即使只是想修改变量b，也必须等待该缓存行从线程A的缓存同步过来。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">推荐</span>
                                <h7>方案A: 使用 @Contended 注解 (Java 8+)</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong>：Java 8
                                  引入了官方解决方案。只要在变量或类上加上
                                  <code>@sun.misc.Contended</code> 注解（需要开启JVM参数
                                  <code>-XX:-RestrictContended</code>），
                                  JVM就会在编译时自动进行缓存行填充。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>官方支持，代码更整洁</li>
                                      <li>意图更明确，易于理解</li>
                                      <li>自动处理缓存行大小</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>是一个受限制的API</li>
                                      <li>需要特定JVM参数</li>
                                      <li>未来可能有变动</li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div class="solution-option alternative">
                              <div class="solution-header">
                                <span class="solution-badge">备选</span>
                                <h7>方案B: 缓存行填充 (Cache Line Padding)</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong
                                  >：手动在两个变量之间填充一些无用的字节（padding），
                                  确保它们落在不同的缓存行里。在Java中，可以通过填充一些无用的
                                  <code>long</code> 成员变量来实现。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>能有效解决伪共享问题</li>
                                      <li>兼容性好，适用于旧版本JVM</li>
                                      <li>性能提升明显</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>代码变得丑陋且难以理解</li>
                                      <li>需要了解目标CPU的缓存行大小</li>
                                      <li>维护成本高</li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div class="trade-offs">
                              <h6>权衡分析</h6>
                              <div class="conclusion">
                                <strong>业界选择：</strong>
                                Disruptor
                                框架是伪共享优化的一个经典案例。在业务代码中，通常不会直接用到如此底层的优化。
                                但理解这个概念至关重要，它能帮助你理解为什么某些并发框架（如
                                <code>LongAdder</code>）的性能如此之高——
                                它们在设计上已经规避了伪共享问题。
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: Java垃圾回收机制 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="Java 性能黑盒之一：垃圾回收 (The GC Black Box)"
                :concept-data="gcBlackBoxData"
                @interaction="handleInteraction"
              >
                <div class="gc-blackbox-showcase">
                  <h3>🗑️ Java 性能黑盒之一：垃圾回收</h3>

                  <div class="gc-intro">
                    <div class="concept-definition">
                      <h4>💡 概念定义</h4>
                      <div class="definition-content">
                        <p>
                          垃圾回收（GC）是Java虚拟机自动管理内存的机制，它负责识别和回收不再被程序使用的对象所占用的内存空间。
                          GC是影响Java应用性能的最重要因素之一，特别是在高并发、大内存的应用场景中。
                        </p>
                        <div class="human-explanation">
                          <h5>人话版解释</h5>
                          <p>
                            GC就像一个勤劳的清洁工，定期清理你程序中不再需要的"垃圾"对象。
                            但这个清洁工有个坏习惯：清理时会让整个程序暂停工作（Stop-The-World），
                            所以我们需要选择合适的清洁工和清洁策略。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="generational-hypothesis">
                    <h4>🧬 分代假说 (Generational Hypothesis)</h4>
                    <div class="hypothesis-explanation">
                      <div class="hypothesis-content">
                        <p>分代假说是现代GC设计的理论基础，它基于两个重要观察：</p>
                        <div class="hypothesis-points">
                          <div class="hypothesis-point">
                            <div class="point-icon">👶</div>
                            <div class="point-content">
                              <h5>弱分代假说</h5>
                              <p>大多数对象都是朝生夕死的，即大部分对象在分配后很快就会变成垃圾</p>
                            </div>
                          </div>
                          <div class="hypothesis-point">
                            <div class="point-icon">👴</div>
                            <div class="point-content">
                              <h5>强分代假说</h5>
                              <p>熬过越多次垃圾收集过程的对象就越难以消亡，存活时间越长</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="memory-regions">
                        <h5>📦 内存区域划分</h5>
                        <div class="regions-visualization">
                          <div class="memory-region young">
                            <div class="region-header">
                              <h6>年轻代 (Young Generation)</h6>
                              <span class="region-size">~1/3 堆内存</span>
                            </div>
                            <div class="region-content">
                              <div class="sub-region eden">
                                <h7>Eden区</h7>
                                <p>新对象分配的地方</p>
                              </div>
                              <div class="sub-region survivor">
                                <h7>Survivor区 (S0, S1)</h7>
                                <p>存放经过一次GC仍存活的对象</p>
                              </div>
                            </div>
                          </div>

                          <div class="memory-arrow">→</div>

                          <div class="memory-region old">
                            <div class="region-header">
                              <h6>老年代 (Old Generation)</h6>
                              <span class="region-size">~2/3 堆内存</span>
                            </div>
                            <div class="region-content">
                              <p>存放长期存活的对象</p>
                              <p>从年轻代晋升而来</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="gc-process-section">
                    <h4>🔄 GC核心阶段</h4>
                    <div class="gc-phases">
                      <div class="phase-card mark">
                        <div class="phase-icon">🔍</div>
                        <h5>标记阶段 (Mark)</h5>
                        <p>从GC Roots开始，标记所有可达的对象</p>
                        <div class="phase-details">
                          <ul>
                            <li>遍历对象引用图</li>
                            <li>标记存活对象</li>
                            <li>识别垃圾对象</li>
                          </ul>
                        </div>
                      </div>

                      <div class="phase-arrow">→</div>

                      <div class="phase-card sweep">
                        <div class="phase-icon">🧹</div>
                        <h5>清除阶段 (Sweep)</h5>
                        <p>回收未被标记的垃圾对象内存</p>
                        <div class="phase-details">
                          <ul>
                            <li>释放垃圾对象内存</li>
                            <li>整理内存碎片</li>
                            <li>更新空闲列表</li>
                          </ul>
                        </div>
                      </div>

                      <div class="phase-arrow">→</div>

                      <div class="phase-card stw">
                        <div class="phase-icon">⏸️</div>
                        <h5>Stop-The-World</h5>
                        <p>GC期间暂停所有应用线程</p>
                        <div class="phase-details">
                          <ul>
                            <li>确保对象引用一致性</li>
                            <li>避免并发修改冲突</li>
                            <li>影响应用响应时间</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="gc-collectors-section">
                    <h4>🛠️ 主流垃圾收集器对比</h4>
                    <div class="collectors-comparison">
                      <div class="collector-card parallel">
                        <div class="collector-header">
                          <div class="collector-icon">⚡</div>
                          <h5>Parallel GC</h5>
                          <span class="collector-tag throughput">吞吐量优先</span>
                        </div>
                        <div class="collector-content">
                          <div class="collector-description">
                            <p>多线程并行收集，适合批处理和计算密集型应用</p>
                          </div>
                          <div class="collector-features">
                            <h6>特点</h6>
                            <ul>
                              <li>高吞吐量，低CPU开销</li>
                              <li>STW时间较长</li>
                              <li>适合后台批处理</li>
                            </ul>
                          </div>
                          <div class="collector-scenarios">
                            <h6>适用场景</h6>
                            <ul>
                              <li>数据分析和批处理</li>
                              <li>科学计算应用</li>
                              <li>对延迟不敏感的服务</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="vs-divider">VS</div>

                      <div class="collector-card g1">
                        <div class="collector-header">
                          <div class="collector-icon">🎯</div>
                          <h5>G1 GC</h5>
                          <span class="collector-tag latency">低延迟优先</span>
                        </div>
                        <div class="collector-content">
                          <div class="collector-description">
                            <p>分区收集，可预测的停顿时间，适合大内存应用</p>
                          </div>
                          <div class="collector-features">
                            <h6>特点</h6>
                            <ul>
                              <li>可控的停顿时间</li>
                              <li>适合大堆内存</li>
                              <li>并发标记和清理</li>
                            </ul>
                          </div>
                          <div class="collector-scenarios">
                            <h6>适用场景</h6>
                            <ul>
                              <li>Web应用和在线服务</li>
                              <li>大内存应用(>4GB)</li>
                              <li>对延迟敏感的系统</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Chapter 7 Mind Map -->
                  <div class="chapter-mindmap">
                    <h4>🧠 第七章知识图谱</h4>
                    <div class="mindmap-container">
                      <div class="mermaid">
                        graph TD A[Java性能优化] --> B[核心理念] A --> C[度量术语] A --> D[硬件挑战]
                        A --> E[垃圾回收] A --> F[JIT编译] A --> G[分析工具] B --> B1[度量驱动] B
                        --> B2[科学方法] B --> B3[S.M.A.R.T流程] C --> C1[延迟Latency] C -->
                        C2[吞吐量Throughput] C --> C3[利用率Utilization] C --> C4[利特尔法则] D -->
                        D1[内存层级] D --> D2[缓存未命中] D --> D3[数据局部性] D --> D4[伪共享] E
                        --> E1[分代假说] E --> E2[内存区域] E --> E3[GC阶段] E --> E4[收集器对比] F
                        --> F1[动态编译] F --> F2[方法内联] F --> F3[分层编译] G --> G1[JFR记录器] G
                        --> G2[JMC分析] G --> G3[性能剖析] classDef philosophy fill:#e1f5fe classDef
                        terminology fill:#f3e5f5 classDef hardware fill:#fff3e0 classDef gc
                        fill:#e8f5e8 classDef jit fill:#fce4ec classDef tools fill:#f1f8e9 class
                        B,B1,B2,B3 philosophy class C,C1,C2,C3,C4 terminology class D,D1,D2,D3,D4
                        hardware class E,E1,E2,E3,E4 gc class F,F1,F2,F3 jit class G,G1,G2,G3 tools
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: JIT即时编译器 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="Java 性能黑盒之二：即时编译 (The JIT Black Box)"
                :concept-data="jitBlackBoxData"
                @interaction="handleInteraction"
              >
                <div class="jit-blackbox-showcase">
                  <h3>⚡ Java 性能黑盒之二：即时编译</h3>

                  <div class="jit-intro">
                    <div class="concept-definition">
                      <h4>💡 概念定义</h4>
                      <div class="definition-content">
                        <p>
                          即时编译器（Just-In-Time Compiler, JIT）是 JVM
                          的核心组件，它在程序运行时， 将频繁执行的"热点"Java
                          字节码动态地编译成本地机器码，从而极大地提升执行效率。
                        </p>
                        <div class="human-explanation">
                          <h5>人话版解释</h5>
                          <p>
                            JVM 最初像一个"翻译官"（解释器），逐句地翻译字节码给CPU听，效率很低。
                            当它发现某段代码被反复执行时，JIT 就会介入，像一个"同声传译"一样，
                            把整段代码一次性翻译成 CPU 能直接听懂的机器语言，并把结果缓存起来。
                            下次再执行这段代码时，就直接运行机器码，速度飞快。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="tiered-compilation">
                    <h4>🏗️ 分层编译 (Tiered Compilation)</h4>
                    <div class="compilation-tiers">
                      <div class="tier-flow">
                        <div class="tier-stage interpreter">
                          <div class="stage-icon">🐌</div>
                          <h5>解释执行</h5>
                          <div class="stage-details">
                            <p>逐行解释字节码</p>
                            <div class="stage-metrics">
                              <span class="metric speed">速度: 慢</span>
                              <span class="metric startup">启动: 快</span>
                            </div>
                          </div>
                        </div>

                        <div class="tier-arrow">→</div>

                        <div class="tier-stage c1">
                          <div class="stage-icon">🚗</div>
                          <h5>C1 编译器</h5>
                          <div class="stage-details">
                            <p>快速编译，基础优化</p>
                            <div class="stage-metrics">
                              <span class="metric speed">速度: 中等</span>
                              <span class="metric optimization">优化: 基础</span>
                            </div>
                            <div class="optimization-list">
                              <ul>
                                <li>方法内联</li>
                                <li>常量折叠</li>
                                <li>死代码消除</li>
                              </ul>
                            </div>
                          </div>
                        </div>

                        <div class="tier-arrow">→</div>

                        <div class="tier-stage c2">
                          <div class="stage-icon">🚀</div>
                          <h5>C2 编译器</h5>
                          <div class="stage-details">
                            <p>深度优化，激进编译</p>
                            <div class="stage-metrics">
                              <span class="metric speed">速度: 极快</span>
                              <span class="metric optimization">优化: 激进</span>
                            </div>
                            <div class="optimization-list">
                              <ul>
                                <li>逃逸分析</li>
                                <li>循环展开</li>
                                <li>单态分派</li>
                                <li>向量化</li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="key-optimizations">
                    <h4>🔧 核心优化技术</h4>
                    <div class="optimizations-grid">
                      <div class="optimization-card inlining">
                        <div class="optimization-header">
                          <div class="optimization-icon">📎</div>
                          <h5>方法内联 (Inlining)</h5>
                          <span class="optimization-importance">最重要</span>
                        </div>
                        <div class="optimization-content">
                          <div class="optimization-description">
                            <p>
                              将被频繁调用的短小方法体直接"复制粘贴"到调用者代码中，消除方法调用开销
                            </p>
                          </div>
                          <div class="optimization-example">
                            <h6>优化示例</h6>
                            <div class="code-comparison">
                              <div class="code-before">
                                <h7>优化前</h7>
                                <pre><code>for (int i = 0; i < 100000; i++) {
  int r = circle.getRadius();
  sum += Math.PI * r * r;
}</code></pre>
                              </div>
                              <div class="code-after">
                                <h7>JIT优化后</h7>
                                <pre><code>int r = circle.radius; // 直接访问字段
for (int i = 0; i < 100000; i++) {
  sum += Math.PI * r * r;
}</code></pre>
                              </div>
                            </div>
                          </div>
                          <div class="optimization-benefits">
                            <h6>优化效果</h6>
                            <ul>
                              <li>消除方法调用开销</li>
                              <li>为其他优化创造条件</li>
                              <li>提升缓存友好度</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="optimization-card devirtualization">
                        <div class="optimization-header">
                          <div class="optimization-icon">🎯</div>
                          <h5>去虚化 (Devirtualization)</h5>
                          <span class="optimization-importance">关键</span>
                        </div>
                        <div class="optimization-content">
                          <div class="optimization-description">
                            <p>
                              当JIT发现接口或虚方法在运行时只有一个实现时，将虚方法调用优化为直接方法调用
                            </p>
                          </div>
                          <div class="optimization-conditions">
                            <h6>触发条件</h6>
                            <ul>
                              <li>单态分派：只有一个实现类</li>
                              <li>双态分派：只有两个实现类</li>
                              <li>运行时类型信息充足</li>
                            </ul>
                          </div>
                          <div class="optimization-benefits">
                            <h6>优化效果</h6>
                            <ul>
                              <li>消除虚方法查找开销</li>
                              <li>启用进一步内联优化</li>
                              <li>提升分支预测准确性</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="optimization-card escape-analysis">
                        <div class="optimization-header">
                          <div class="optimization-icon">🔍</div>
                          <h5>逃逸分析 (Escape Analysis)</h5>
                          <span class="optimization-importance">高级</span>
                        </div>
                        <div class="optimization-content">
                          <div class="optimization-description">
                            <p>
                              分析对象的作用域，如果对象不会"逃逸"出方法，则可以进行栈上分配或标量替换
                            </p>
                          </div>
                          <div class="optimization-techniques">
                            <h6>优化技术</h6>
                            <ul>
                              <li>栈上分配：避免堆分配开销</li>
                              <li>标量替换：用基本类型替换对象</li>
                              <li>锁消除：消除不必要的同步</li>
                            </ul>
                          </div>
                          <div class="optimization-benefits">
                            <h6>优化效果</h6>
                            <ul>
                              <li>减少GC压力</li>
                              <li>提升内存访问效率</li>
                              <li>消除同步开销</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="deoptimization-section">
                    <h4>⚠️ 逆优化 (Deoptimization)</h4>
                    <div class="deoptimization-explanation">
                      <div class="deoptimization-concept">
                        <h5>概念说明</h5>
                        <p>
                          JIT的激进优化是建立在一些"假设"之上的。当这些假设在运行时被打破时，
                          JVM会废弃已编译的优化代码，退回到解释执行状态，这会导致性能的急剧下降。
                        </p>
                      </div>

                      <div class="deoptimization-triggers">
                        <h5>触发条件</h5>
                        <div class="triggers-grid">
                          <div class="trigger-item">
                            <div class="trigger-icon">📦</div>
                            <h6>类加载</h6>
                            <p>加载新类使得接口有了第二个实现</p>
                          </div>
                          <div class="trigger-item">
                            <div class="trigger-icon">🔄</div>
                            <h6>类型变化</h6>
                            <p>运行时类型分布发生显著变化</p>
                          </div>
                          <div class="trigger-item">
                            <div class="trigger-icon">🎭</div>
                            <h6>多态增加</h6>
                            <p>从单态变为多态调用</p>
                          </div>
                        </div>
                      </div>

                      <div class="deoptimization-prevention">
                        <h5>预防策略</h5>
                        <div class="prevention-tips">
                          <div class="tip-item">
                            <div class="tip-icon">🎯</div>
                            <h6>保持类型稳定</h6>
                            <p>避免在热点代码路径中引入新的类型</p>
                          </div>
                          <div class="tip-item">
                            <div class="tip-icon">📏</div>
                            <h6>控制方法大小</h6>
                            <p>保持方法体小而专注，提高内联成功率</p>
                          </div>
                          <div class="tip-item">
                            <div class="tip-icon">🔒</div>
                            <h6>减少多态性</h6>
                            <p>在性能关键路径上尽量使用具体类型</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 6: 现代性能分析工具 -->
            <section id="topic-5" class="topic-section" ref="topic5">
              <ExpandableSection
                title="现代化的性能分析武器：JFR 与 JMC (Modern Profiling Arsenal: JFR & JMC)"
                :concept-data="jfrJmcData"
                @interaction="handleInteraction"
              >
                <div class="jfr-jmc-showcase">
                  <h3>🛠️ 现代化的性能分析武器：JFR 与 JMC</h3>

                  <div class="tools-intro">
                    <div class="concept-definition">
                      <h4>💡 概念定义</h4>
                      <div class="definition-content">
                        <div class="tools-overview">
                          <div class="tool-card jfr">
                            <div class="tool-icon">📊</div>
                            <h5>JDK 飞行记录器 (JFR)</h5>
                            <p>内置于 HotSpot JVM 内部的、低开销的、事件驱动的性能剖析框架</p>
                          </div>
                          <div class="tool-connector">+</div>
                          <div class="tool-card jmc">
                            <div class="tool-icon">🔍</div>
                            <h5>JDK 任务控制器 (JMC)</h5>
                            <p>用于对 JFR 收集到的数据进行可视化分析的桌面工具</p>
                          </div>
                        </div>
                        <div class="human-explanation">
                          <h5>人话版解释</h5>
                          <p>
                            JFR + JMC 是 Java 官方提供的"飞机黑匣子"。JFR 就是那个黑匣子，
                            始终在后台记录着飞机（JVM）的各种飞行参数，而且几乎不影响飞机飞行（低开销）。
                            JMC 就是事故调查组的分析软件，当飞机出问题（性能瓶颈）后，
                            可以用它来回放和分析黑匣子里的数据，找出事故原因。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="jfr-features">
                    <h4>🎯 JFR 核心特性</h4>
                    <div class="features-grid">
                      <div class="feature-card low-overhead">
                        <div class="feature-icon">⚡</div>
                        <h5>超低开销</h5>
                        <div class="feature-details">
                          <p>性能开销通常 &lt; 1%，可在生产环境长期运行</p>
                          <div class="feature-specs">
                            <span class="spec">事件驱动设计</span>
                            <span class="spec">线程本地缓冲</span>
                            <span class="spec">异步写入</span>
                          </div>
                        </div>
                      </div>

                      <div class="feature-card always-on">
                        <div class="feature-icon">🔄</div>
                        <h5>永远在线</h5>
                        <div class="feature-details">
                          <p>设计为可持续运行的监控工具，捕捉瞬时问题</p>
                          <div class="feature-specs">
                            <span class="spec">滚动记录</span>
                            <span class="spec">自动清理</span>
                            <span class="spec">内存控制</span>
                          </div>
                        </div>
                      </div>

                      <div class="feature-card comprehensive">
                        <div class="feature-icon">📊</div>
                        <h5>全面覆盖</h5>
                        <div class="feature-details">
                          <p>记录JVM内部所有关键事件和性能指标</p>
                          <div class="feature-specs">
                            <span class="spec">GC事件</span>
                            <span class="spec">JIT编译</span>
                            <span class="spec">线程活动</span>
                            <span class="spec">I/O操作</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="jmc-capabilities">
                    <h4>🔍 JMC 分析能力</h4>
                    <div class="capabilities-showcase">
                      <div class="capability-section automated">
                        <div class="capability-header">
                          <div class="capability-icon">🤖</div>
                          <h5>自动化分析</h5>
                        </div>
                        <div class="capability-content">
                          <p>智能识别常见性能问题并提供修复建议</p>
                          <div class="analysis-types">
                            <div class="analysis-item">
                              <span class="analysis-name">内存泄漏检测</span>
                              <span class="analysis-desc">识别持续增长的内存使用</span>
                            </div>
                            <div class="analysis-item">
                              <span class="analysis-name">GC压力分析</span>
                              <span class="analysis-desc">评估垃圾回收对性能的影响</span>
                            </div>
                            <div class="analysis-item">
                              <span class="analysis-name">热点方法识别</span>
                              <span class="analysis-desc">找出CPU消耗最多的代码路径</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="capability-section detailed">
                        <div class="capability-header">
                          <div class="capability-icon">🔬</div>
                          <h5>深度剖析</h5>
                        </div>
                        <div class="capability-content">
                          <p>提供多维度的性能数据可视化和钻取分析</p>
                          <div class="analysis-views">
                            <div class="view-item">
                              <span class="view-name">方法剖析</span>
                              <span class="view-desc">CPU时间分布和调用栈分析</span>
                            </div>
                            <div class="view-item">
                              <span class="view-name">内存分析</span>
                              <span class="view-desc">对象分配和GC活动详情</span>
                            </div>
                            <div class="view-item">
                              <span class="view-name">线程分析</span>
                              <span class="view-desc">线程状态和锁竞争情况</span>
                            </div>
                            <div class="view-item">
                              <span class="view-name">I/O分析</span>
                              <span class="view-desc">文件和网络操作性能</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions for JFR/JMC -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>常见问题：线上服务偶发性能抖动</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              一个线上服务偶尔会出现短暂的性能抖动，导致部分用户请求超时。
                              这个问题难以复现，且发生时研发人员往往不在现场，事后查看常规日志又看不出任何端倪。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              <strong>缺乏在问题发生瞬间捕捉系统全面状态的手段</strong>。
                              传统的监控只能看到CPU、内存等宏观指标，无法深入到JVM内部，
                              比如在那一刻到底是哪个锁产生了竞争，还是发生了一次短暂的GC。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案：生产环境常态化JFR监控</h6>

                            <div class="solution-steps">
                              <div class="step-item">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                  <h7>启用JFR守护进程模式</h7>
                                  <div class="step-details">
                                    <p>以守护进程模式启动JFR，让其在后台持续记录，并设置滚动策略</p>
                                    <div class="code-example">
                                      <pre><code>-XX:StartFlightRecording=disk=true,dumponexit=true,maxage=6h,settings=profile</code></pre>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div class="step-item">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                  <h7>建立告警响应机制</h7>
                                  <div class="step-details">
                                    <p>
                                      当监控系统告警或用户反馈问题时，立刻从服务器上拉取问题时间点附近的.jfr文件
                                    </p>
                                    <div class="response-workflow">
                                      <span class="workflow-step">告警触发</span>
                                      <span class="workflow-arrow">→</span>
                                      <span class="workflow-step">拉取JFR文件</span>
                                      <span class="workflow-arrow">→</span>
                                      <span class="workflow-step">JMC分析</span>
                                      <span class="workflow-arrow">→</span>
                                      <span class="workflow-step">问题定位</span>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div class="step-item">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                  <h7>使用JMC进行事后分析</h7>
                                  <div class="step-details">
                                    <p>精确回溯问题发生前后几分钟内，JVM内部的所有关键事件</p>
                                    <div class="analysis-targets">
                                      <span class="target-item">GC停顿时间</span>
                                      <span class="target-item">锁争用情况</span>
                                      <span class="target-item">热点方法变化</span>
                                      <span class="target-item">JIT编译活动</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div class="trade-offs">
                              <h6>权衡分析</h6>
                              <div class="pros-cons">
                                <div class="pros">
                                  <h8>优点</h8>
                                  <ul>
                                    <li>能够捕捉瞬时、难以复现的生产问题</li>
                                    <li>提供前所未有的高保真度性能数据</li>
                                    <li>是解决棘手线上性能问题的最强武器</li>
                                  </ul>
                                </div>
                                <div class="cons">
                                  <h8>缺点</h8>
                                  <ul>
                                    <li>需要一定的磁盘空间存储JFR文件</li>
                                    <li>对分析人员的能力有一定要求</li>
                                    <li>需要建立完善的监控和响应流程</li>
                                  </ul>
                                </div>
                              </div>
                              <div class="conclusion">
                                <strong>业界选择：</strong>
                                对于所有核心的、对性能敏感的Java服务，
                                <strong>在生产环境中开启JFR已经成为一种事实上的最佳实践</strong>。
                                它所带来的深度洞察能力，是其他任何工具都无法比拟的。
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="best-practices">
                    <h4>📋 JFR/JMC 最佳实践</h4>
                    <div class="practices-grid">
                      <div class="practice-card production">
                        <div class="practice-header">
                          <div class="practice-icon">🏭</div>
                          <h5>生产环境配置</h5>
                        </div>
                        <div class="practice-content">
                          <div class="practice-tips">
                            <div class="tip">
                              <span class="tip-label">配置选择</span>
                              <span class="tip-desc"
                                >使用default配置进行常态化监控，profile配置用于问题诊断</span
                              >
                            </div>
                            <div class="tip">
                              <span class="tip-label">存储管理</span>
                              <span class="tip-desc"
                                >设置合理的maxage和maxsize，避免磁盘空间耗尽</span
                              >
                            </div>
                            <div class="tip">
                              <span class="tip-label">性能验证</span>
                              <span class="tip-desc">在生产环境部署前进行充分的性能基准测试</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="practice-card analysis">
                        <div class="practice-header">
                          <div class="practice-icon">🔍</div>
                          <h5>分析技巧</h5>
                        </div>
                        <div class="practice-content">
                          <div class="practice-tips">
                            <div class="tip">
                              <span class="tip-label">自动分析优先</span>
                              <span class="tip-desc">首先查看JMC的Automated Analysis Results</span>
                            </div>
                            <div class="tip">
                              <span class="tip-label">时间窗口聚焦</span>
                              <span class="tip-desc">缩小分析时间窗口到问题发生的具体时段</span>
                            </div>
                            <div class="tip">
                              <span class="tip-label">多维度关联</span>
                              <span class="tip-desc">结合GC、JIT、线程等多个视图进行综合分析</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="practice-card team">
                        <div class="practice-header">
                          <div class="practice-icon">👥</div>
                          <h5>团队协作</h5>
                        </div>
                        <div class="practice-content">
                          <div class="practice-tips">
                            <div class="tip">
                              <span class="tip-label">技能培训</span>
                              <span class="tip-desc">确保团队成员掌握JFR/JMC的基本使用方法</span>
                            </div>
                            <div class="tip">
                              <span class="tip-label">标准化流程</span>
                              <span class="tip-desc">建立标准的性能问题排查和分析流程</span>
                            </div>
                            <div class="tip">
                              <span class="tip-label">知识沉淀</span>
                              <span class="tip-desc">记录典型问题的分析过程和解决方案</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>
          </main>
        </div>
      </div>
    </div>

    <!-- 返回顶部按钮 -->
    <BackToTopButton />

    <!-- 浮动章节菜单 -->
    <FloatingChapterMenu />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'
import BackToTopButton from '@/components/BackToTopButton.vue'
import FloatingChapterMenu from '@/components/FloatingChapterMenu.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)

// 课程主题
const courseTopics = ref([
  {
    title: '性能优化的核心理念',
    description: '建立科学的性能分析方法论',
  },
  {
    title: '性能度量关键术语',
    description: '掌握性能工程的通用语言',
  },
  {
    title: '硬件挑战与瓶颈',
    description: '理解现代计算机的物理限制',
  },
  {
    title: 'Java垃圾回收机制',
    description: '深入GC的工作原理与优化',
  },
  {
    title: 'JIT即时编译器',
    description: '探索动态优化的奥秘',
  },
  {
    title: '现代性能分析工具',
    description: '掌握JFR与JMC的使用',
  },
])

// 概念数据
const performancePhilosophyData = ref({
  title: '性能优化的核心理念',
  keyPoints: [
    'Java 性能优化是一门实验科学，核心是通过精确的度量来识别和解决系统瓶颈',
    '"你无法优化你无法度量的东西" - 人类直觉在判断复杂系统瓶颈时几乎总是出错',
    '应该关注宏观的用户体验指标（如99%响应时间），而不是孤立的微观指标',
    '建立科学的性能分析流程：定义目标 → 建立基线 → 分析瓶颈 → 单点优化 → 回归测试',
    '避免"过早优化"和"性能技巧大全"的误区，理解原理比背诵技巧更重要',
  ],
  interactiveElements: [
    { type: 'demo-measurement', label: '演示度量工具' },
    { type: 'show-smart-process', label: 'S.M.A.R.T.流程' },
  ],
})

const performanceTerminologyData = ref({
  title: '性能度量关键术语',
  keyPoints: [
    '延迟(Latency)：处理单个请求所需的时间，通常关注P99延迟而非平均值',
    '吞吐量(Throughput)：单位时间内系统能处理的请求数量，如QPS',
    '利用率(Utilization)：资源被使用的时间百分比，过高可能导致性能衰减',
    '可伸缩性(Scalability)：增加资源时吞吐量提升的能力，受系统架构限制',
    '利特尔法则：系统中的平均请求数 = 请求到达率 × 平均请求处理时间',
  ],
  interactiveElements: [
    { type: 'metrics-calculator', label: '性能指标计算器' },
    { type: 'littles-law-demo', label: '利特尔法则演示' },
  ],
})

const hardwareChallengeData = ref({
  title: '性能瓶颈的根源：硬件的挑战',
  keyPoints: [
    '现代Java性能问题的核心是处理器速度与内存访问速度之间的巨大鸿沟',
    '内存层级结构：CPU寄存器(~0.3ns) → L1缓存(~1ns) → L2缓存(~3-5ns) → L3缓存(~10-20ns) → 主存(~50-100ns)',
    '缓存未命中(Cache Miss)是性能的巨大杀手，一个主存访问的延迟足够CPU执行上百条指令',
    '数据局部性原理：时间局部性(最近访问的数据很可能再次被访问)和空间局部性(相邻数据很可能被访问)',
    '伪共享(False Sharing)：多核CPU中不相关变量位于同一缓存行导致的无谓同步开销',
  ],
  interactiveElements: [
    { type: 'memory-hierarchy-demo', label: '内存层级演示' },
    { type: 'cache-miss-simulation', label: '缓存未命中模拟' },
    { type: 'false-sharing-demo', label: '伪共享演示' },
  ],
})

const gcBlackBoxData = ref({
  title: 'Java 性能黑盒之一：垃圾回收',
  keyPoints: [
    '垃圾回收(GC)是Java虚拟机自动管理内存的机制，是影响性能的最重要因素之一',
    '分代假说：大多数对象朝生夕死(弱分代假说)，存活越久的对象越难消亡(强分代假说)',
    '内存区域：年轻代(Eden区+Survivor区)存放新对象，老年代存放长期存活对象',
    'GC核心阶段：标记(Mark)识别垃圾对象，清除(Sweep)回收内存，可能伴随Stop-The-World暂停',
    '主流收集器：Parallel GC(吞吐量优先) vs G1 GC(低延迟优先)，各有适用场景',
  ],
  interactiveElements: [
    { type: 'gc-process-demo', label: 'GC过程演示' },
    { type: 'generational-demo', label: '分代回收演示' },
    { type: 'gc-tuning-simulator', label: 'GC调优模拟器' },
  ],
})

const jitBlackBoxData = ref({
  title: 'Java 性能黑盒之二：即时编译',
  keyPoints: [
    'JIT即时编译器将频繁执行的"热点"字节码动态编译成本地机器码，极大提升执行效率',
    '分层编译：解释执行 → C1编译器(快速编译) → C2编译器(深度优化)的渐进优化过程',
    '方法内联是最重要的JIT优化，消除方法调用开销并为其他优化创造条件',
    '去虚化优化：将虚方法调用优化为直接调用，逃逸分析实现栈上分配和锁消除',
    '逆优化风险：当JIT的假设被打破时会退回解释执行，需要保持代码类型稳定性',
  ],
  interactiveElements: [
    { type: 'jit-compilation-demo', label: 'JIT编译演示' },
    { type: 'optimization-showcase', label: '优化技术展示' },
    { type: 'deoptimization-simulator', label: '逆优化模拟器' },
  ],
})

const jfrJmcData = ref({
  title: '现代化的性能分析武器：JFR 与 JMC',
  keyPoints: [
    'JFR是内置于JVM的低开销(<1%)事件驱动性能剖析框架，可在生产环境长期运行',
    'JMC是JFR数据的可视化分析工具，提供自动化分析和深度性能洞察',
    '事件模型：JVM内部关键活动(GC、JIT、锁竞争等)都被埋点记录到线程本地缓冲区',
    '生产可用：设计为"永远在线"工具，能捕捉转瞬即逝的性能问题',
    '业界最佳实践：在核心服务中开启JFR常态化监控，事后分析疑难性能问题',
  ],
  interactiveElements: [
    { type: 'jfr-recording-demo', label: 'JFR记录演示' },
    { type: 'jmc-analysis-tour', label: 'JMC分析导览' },
    { type: 'production-monitoring', label: '生产监控实践' },
  ],
})

// 方法
const scrollToTopic = (index: number) => {
  const element = document.getElementById(`topic-${index}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
    currentTopic.value = index
  }
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
  // 这里可以添加具体的交互逻辑
}

const toggleNotes = () => {
  console.log('Toggle notes')
}

const showQuiz = () => {
  console.log('Show quiz')
}

const updateProgress = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollHeight = document.documentElement.scrollHeight - window.innerHeight
  const scrolled = (scrollTop / scrollHeight) * 100
  progress.value = Math.min(Math.max(scrolled, 0), 100)
}

const updateCurrentTopic = () => {
  const topics = courseTopics.value
  for (let i = topics.length - 1; i >= 0; i--) {
    const element = document.getElementById(`topic-${i}`)
    if (element) {
      const rect = element.getBoundingClientRect()
      if (rect.top <= 100) {
        currentTopic.value = i
        break
      }
    }
  }
}

const handleScroll = () => {
  updateProgress()
  updateCurrentTopic()
}

onMounted(async () => {
  window.addEventListener('scroll', handleScroll)
  updateProgress()

  // 初始化Mermaid
  try {
    console.log('开始初始化 Mermaid...')
    const mermaid = await import('mermaid')
    console.log('Mermaid 模块加载成功:', mermaid)

    mermaid.default.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    })
    console.log('Mermaid 初始化完成')

    // 延迟渲染以确保DOM已加载
    setTimeout(async () => {
      try {
        console.log('开始渲染 Mermaid 图表...')

        // 查找所有mermaid元素并渲染
        const mermaidElements = document.querySelectorAll('.mermaid')
        for (let i = 0; i < mermaidElements.length; i++) {
          const element = mermaidElements[i]
          const content = element.textContent?.trim()
          if (content) {
            try {
              const { svg } = await mermaid.default.render(`chapter7-mermaid-${i}`, content)
              element.innerHTML = svg
              console.log(`Mermaid 图表 ${i} 渲染完成`)
            } catch (renderError) {
              console.error(`Mermaid 图表 ${i} 渲染错误:`, renderError)
            }
          }
        }
      } catch (renderError) {
        console.error('Mermaid 渲染错误:', renderError)
      }
    }, 1000)
  } catch (error) {
    console.error('Mermaid 初始化失败:', error)
  }
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.java-chapter7 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.chapter-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.chapter-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.chapter-badge {
  display: inline-block;
  margin-bottom: 2rem;
}

.badge-text {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.progress-bar {
  max-width: 400px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 8px;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  transition: width 0.3s ease;
  border-radius: 10px;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8rem;
  font-weight: 500;
}

.content-wrapper {
  padding: 3rem 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  align-items: start;
}

.sidebar {
  position: sticky;
  top: 2rem;
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.outline-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.outline-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.outline-item:hover {
  background: #f8f9fa;
  border-color: #e9ecef;
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

.outline-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.95rem;
  font-weight: 600;
}

.outline-content p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.7;
  line-height: 1.4;
}

.toolbar {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tool-button {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  text-align: left;
}

.tool-button:hover {
  background: #e9ecef;
  border-color: #667eea;
  color: #667eea;
}

.main-content {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.topic-section {
  margin-bottom: 0;
}

.topic-section:not(:last-child) {
  border-bottom: 1px solid #e9ecef;
}

/* Performance Philosophy Styles */
.performance-philosophy-showcase {
  padding: 2rem;
}

.performance-philosophy-showcase h3 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.philosophy-intro {
  margin-bottom: 3rem;
}

.concept-definition {
  background: #f8f9ff;
  border-left: 4px solid #667eea;
  padding: 2rem;
  border-radius: 0 10px 10px 0;
  margin-bottom: 2rem;
}

.concept-definition h4 {
  color: #667eea;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.definition-content p {
  line-height: 1.7;
  margin-bottom: 1rem;
}

.human-explanation {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.human-explanation h5 {
  color: #856404;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

.human-explanation p {
  color: #856404;
  margin: 0;
  font-style: italic;
}

.core-principles h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.principles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.principle-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.principle-card:hover {
  border-color: #667eea;
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.principle-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.principle-card h5 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.principle-card p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.analogy-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.analogy-section h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.analogy-comparison {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
}

.comparison-item {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.comparison-item.wrong {
  border-left: 4px solid #dc3545;
}

.comparison-item.correct {
  border-left: 4px solid #28a745;
}

.comparison-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.comparison-icon {
  font-size: 1.5rem;
}

.comparison-header h5 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.comparison-content p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.example {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
}

.example strong {
  color: #667eea;
}

.comparison-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

.real-world-section {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.real-world-section h4 {
  color: #c53030;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.problem-case {
  background: white;
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.case-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.case-icon {
  font-size: 1.5rem;
}

.case-header h5 {
  margin: 0;
  color: #c53030;
  font-size: 1.2rem;
  font-weight: 600;
}

.problem-description,
.root-cause {
  margin-bottom: 2rem;
}

.problem-description h6,
.root-cause h6 {
  color: #333;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.problem-description p,
.root-cause p {
  line-height: 1.7;
  margin: 0;
}

.solutions h6 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.smart-approach {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.smart-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.step-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content h7 {
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: block;
}

.step-content p {
  margin: 0;
  line-height: 1.6;
  color: #666;
}

.trade-offs {
  background: #f0f8ff;
  border: 1px solid #bee5eb;
  border-radius: 8px;
  padding: 1.5rem;
}

.trade-offs h6 {
  color: #0c5460;
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.pros-cons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.pros h7,
.cons h7 {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.pros h7 {
  color: #28a745;
}

.cons h7 {
  color: #dc3545;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1.2rem;
}

.pros li,
.cons li {
  line-height: 1.5;
  margin-bottom: 0.25rem;
}

.conclusion {
  padding-top: 1rem;
  border-top: 1px solid #bee5eb;
  color: #0c5460;
  line-height: 1.6;
}

.common-pitfalls {
  margin: 2rem 0;
}

.common-pitfalls h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.pitfalls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.pitfall-card {
  background: #fff8e1;
  border: 2px solid #ffecb3;
  border-radius: 10px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.pitfall-card:hover {
  border-color: #ffc107;
  box-shadow: 0 5px 15px rgba(255, 193, 7, 0.2);
}

.pitfall-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.pitfall-icon {
  font-size: 1.5rem;
}

.pitfall-header h5 {
  margin: 0;
  color: #e65100;
  font-size: 1.1rem;
  font-weight: 600;
}

.pitfall-content p {
  margin: 0;
  line-height: 1.6;
  color: #bf360c;
}

/* Performance Terminology Styles */
.performance-terminology-showcase {
  padding: 2rem;
}

.performance-terminology-showcase h3 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.terminology-intro {
  background: #f0f8ff;
  border-left: 4px solid #2196f3;
  padding: 1.5rem;
  border-radius: 0 8px 8px 0;
  margin-bottom: 2rem;
}

.terminology-intro p {
  margin: 0;
  line-height: 1.7;
  color: #1565c0;
}

.key-metrics h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.metric-card {
  background: white;
  border: 2px solid #e3f2fd;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2196f3, #21cbf3);
}

.metric-card:hover {
  border-color: #2196f3;
  box-shadow: 0 8px 25px rgba(33, 150, 243, 0.15);
  transform: translateY(-2px);
}

.metric-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.metric-card h5 {
  color: #1565c0;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.metric-definition {
  margin-bottom: 1.5rem;
}

.metric-definition p {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.metric-definition strong {
  color: #1976d2;
}

.metric-details h6 {
  color: #333;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
}

.metric-details ul {
  margin: 0;
  padding-left: 1.2rem;
}

.metric-details li {
  line-height: 1.5;
  margin-bottom: 0.5rem;
  color: #666;
}

/* Little's Law Styles */
.littles-law-section {
  background: #f0f8ff;
  border: 2px solid #e3f2fd;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.littles-law-section h4 {
  color: #1565c0;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.law-explanation {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

.law-formula {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.formula-box h5 {
  color: #1976d2;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.formula-display {
  background: #e3f2fd;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.formula-text {
  font-weight: 600;
  color: #0d47a1;
  font-size: 0.95rem;
}

.formula-variables {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.variable {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.var-symbol {
  width: 24px;
  height: 24px;
  background: #2196f3;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.var-desc {
  color: #666;
  font-size: 0.9rem;
}

.law-implications h5 {
  color: #1976d2;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.implications-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.implication-card {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.implication-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.implication-card h6 {
  color: #1976d2;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.implication-card p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Hardware Challenge Styles */
.hardware-challenge-showcase {
  padding: 2rem;
}

.hardware-challenge-showcase h3 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.challenge-intro {
  margin-bottom: 3rem;
}

.memory-hierarchy {
  margin: 2rem 0;
}

.memory-hierarchy h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.hierarchy-visualization {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
}

.hierarchy-level {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  transition: all 0.3s ease;
  position: relative;
}

.hierarchy-level:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.hierarchy-level.cpu {
  border-left: 4px solid #ff5722;
}

.hierarchy-level.l1 {
  border-left: 4px solid #ff9800;
}

.hierarchy-level.l2 {
  border-left: 4px solid #ffc107;
}

.hierarchy-level.l3 {
  border-left: 4px solid #ffeb3b;
}

.hierarchy-level.ram {
  border-left: 4px solid #4caf50;
}

.hierarchy-level.disk {
  border-left: 4px solid #9e9e9e;
}

.level-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.level-info h5 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.level-stats {
  display: flex;
  gap: 1rem;
}

.latency,
.size {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.level-description p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

.hierarchy-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
  margin: 0.5rem 0;
}

/* Cache Miss and Locality Styles */
.cache-miss-section {
  background: #fff3e0;
  border: 2px solid #ffcc02;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.cache-miss-section h4 {
  color: #e65100;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.miss-analogy {
  margin-bottom: 2rem;
}

.miss-analogy h5 {
  color: #bf360c;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.chef-analogy {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.analogy-level {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.level-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.analogy-level h6 {
  color: #d84315;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.analogy-level p {
  margin: 0;
  color: #666;
  font-size: 0.8rem;
  line-height: 1.4;
}

.locality-principles {
  margin-top: 2rem;
}

.locality-principles h5 {
  color: #bf360c;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.locality-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.locality-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.locality-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.locality-card.temporal {
  border-left: 4px solid #2196f3;
}

.locality-card.spatial {
  border-left: 4px solid #4caf50;
}

.locality-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.locality-card h6 {
  color: #333;
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.locality-card p {
  margin-bottom: 1rem;
  color: #666;
  line-height: 1.6;
}

.locality-example {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 0.75rem;
  font-size: 0.9rem;
}

.locality-example strong {
  color: #667eea;
}

/* GC Black Box Styles */
.gc-blackbox-showcase {
  padding: 2rem;
}

.gc-blackbox-showcase h3 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.gc-intro {
  margin-bottom: 3rem;
}

.generational-hypothesis {
  margin: 2rem 0;
}

.generational-hypothesis h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.hypothesis-explanation {
  background: #f0f8ff;
  border-radius: 12px;
  padding: 2rem;
}

.hypothesis-content p {
  margin-bottom: 1.5rem;
  line-height: 1.7;
  color: #1565c0;
}

.hypothesis-points {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.hypothesis-point {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.point-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.point-content h5 {
  color: #1976d2;
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.point-content p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.memory-regions h5 {
  color: #1976d2;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.regions-visualization {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
}

.memory-region {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.memory-region:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.memory-region.young {
  border-left: 4px solid #4caf50;
}

.memory-region.old {
  border-left: 4px solid #ff9800;
}

.region-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e9ecef;
}

.region-header h6 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.region-size {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.region-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sub-region {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
}

.sub-region h7 {
  display: block;
  color: #333;
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.sub-region p {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
  line-height: 1.5;
}

.memory-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

/* GC Process Styles */
.gc-process-section {
  background: #f0f8ff;
  border: 2px solid #e3f2fd;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.gc-process-section h4 {
  color: #1565c0;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.gc-phases {
  display: grid;
  grid-template-columns: 1fr auto 1fr auto 1fr;
  gap: 1rem;
  align-items: center;
}

.phase-card {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.phase-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.phase-card.mark {
  border-left: 4px solid #2196f3;
}

.phase-card.sweep {
  border-left: 4px solid #4caf50;
}

.phase-card.stw {
  border-left: 4px solid #ff9800;
}

.phase-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.phase-card h5 {
  color: #333;
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.phase-card p {
  margin-bottom: 1rem;
  color: #666;
  line-height: 1.6;
  font-size: 0.9rem;
}

.phase-details ul {
  margin: 0;
  padding-left: 1.2rem;
  text-align: left;
}

.phase-details li {
  color: #666;
  font-size: 0.85rem;
  line-height: 1.5;
  margin-bottom: 0.25rem;
}

.phase-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

/* GC Collectors Styles */
.gc-collectors-section {
  margin: 2rem 0;
}

.gc-collectors-section h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.collectors-comparison {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: stretch;
}

.collector-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.collector-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.collector-card.parallel {
  border-left: 4px solid #ff9800;
}

.collector-card.g1 {
  border-left: 4px solid #4caf50;
}

.collector-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.collector-icon {
  font-size: 2rem;
}

.collector-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
  flex-grow: 1;
}

.collector-tag {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.collector-tag.throughput {
  background: #fff3e0;
  color: #e65100;
}

.collector-tag.latency {
  background: #e8f5e8;
  color: #2e7d32;
}

.collector-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.collector-description p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-style: italic;
}

.collector-features h6,
.collector-scenarios h6 {
  color: #333;
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.collector-features ul,
.collector-scenarios ul {
  margin: 0;
  padding-left: 1.2rem;
}

.collector-features li,
.collector-scenarios li {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.vs-divider {
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  background: #f8f9fa;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Chapter Mind Map Styles */
.chapter-mindmap {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.chapter-mindmap h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  text-align: center;
}

.mindmap-container {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.mermaid {
  text-align: center;
}

/* JIT Black Box Styles */
.jit-blackbox-showcase {
  padding: 2rem;
}

.jit-blackbox-showcase h3 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.jit-intro {
  margin-bottom: 3rem;
}

.tiered-compilation {
  margin: 2rem 0;
}

.tiered-compilation h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.compilation-tiers {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
}

.tier-flow {
  display: grid;
  grid-template-columns: 1fr auto 1fr auto 1fr;
  gap: 1.5rem;
  align-items: center;
}

.tier-stage {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.tier-stage:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.tier-stage.interpreter {
  border-left: 4px solid #9e9e9e;
}

.tier-stage.c1 {
  border-left: 4px solid #ff9800;
}

.tier-stage.c2 {
  border-left: 4px solid #4caf50;
}

.stage-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.tier-stage h5 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.stage-details p {
  margin-bottom: 1rem;
  color: #666;
  line-height: 1.6;
}

.stage-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.metric {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.metric.speed {
  background: #e3f2fd;
  color: #1976d2;
}

.metric.startup {
  background: #f3e5f5;
  color: #7b1fa2;
}

.metric.optimization {
  background: #e8f5e8;
  color: #388e3c;
}

.optimization-list ul {
  margin: 0;
  padding-left: 1.2rem;
  text-align: left;
}

.optimization-list li {
  color: #666;
  font-size: 0.85rem;
  line-height: 1.5;
  margin-bottom: 0.25rem;
}

.tier-arrow {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

/* Key Optimizations Styles */
.key-optimizations {
  margin: 2rem 0;
}

.key-optimizations h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.optimizations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.optimization-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.optimization-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.optimization-card.inlining {
  border-left: 4px solid #4caf50;
}

.optimization-card.devirtualization {
  border-left: 4px solid #2196f3;
}

.optimization-card.escape-analysis {
  border-left: 4px solid #ff9800;
}

.optimization-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.optimization-icon {
  font-size: 2rem;
}

.optimization-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
  flex-grow: 1;
}

.optimization-importance {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  background: #e8f5e8;
  color: #2e7d32;
}

.optimization-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.optimization-description p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.optimization-example h6,
.optimization-benefits h6,
.optimization-conditions h6,
.optimization-techniques h6 {
  color: #333;
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.code-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.code-before,
.code-after {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

.code-before h7,
.code-after h7 {
  display: block;
  color: #333;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.code-before pre,
.code-after pre {
  margin: 0;
  font-size: 0.8rem;
  line-height: 1.4;
  color: #666;
}

.optimization-benefits ul,
.optimization-conditions ul,
.optimization-techniques ul {
  margin: 0;
  padding-left: 1.2rem;
}

.optimization-benefits li,
.optimization-conditions li,
.optimization-techniques li {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

/* Deoptimization Styles */
.deoptimization-section {
  background: #fff3e0;
  border: 2px solid #ffcc02;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.deoptimization-section h4 {
  color: #e65100;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.deoptimization-explanation {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.deoptimization-concept h5,
.deoptimization-triggers h5,
.deoptimization-prevention h5 {
  color: #bf360c;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.deoptimization-concept p {
  margin: 0;
  color: #d84315;
  line-height: 1.7;
}

.triggers-grid,
.prevention-tips {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.trigger-item,
.tip-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.trigger-icon,
.tip-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.trigger-item h6,
.tip-item h6 {
  color: #d84315;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.trigger-item p,
.tip-item p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* JFR/JMC Styles */
.jfr-jmc-showcase {
  padding: 2rem;
}

.jfr-jmc-showcase h3 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
}

.tools-intro {
  margin-bottom: 3rem;
}

.tools-overview {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
  margin-bottom: 2rem;
}

.tool-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.tool-card.jfr {
  border-left: 4px solid #2196f3;
}

.tool-card.jmc {
  border-left: 4px solid #4caf50;
}

.tool-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.tool-card h5 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.tool-card p {
  margin: 0;
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

.tool-connector {
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
  background: #f8f9fa;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* JFR Features Styles */
.jfr-features {
  margin: 2rem 0;
}

.jfr-features h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.feature-card.low-overhead {
  border-left: 4px solid #4caf50;
}

.feature-card.always-on {
  border-left: 4px solid #2196f3;
}

.feature-card.comprehensive {
  border-left: 4px solid #ff9800;
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-card h5 {
  color: #333;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.feature-details p {
  margin-bottom: 1.5rem;
  color: #666;
  line-height: 1.6;
}

.feature-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.spec {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* JMC Capabilities Styles */
.jmc-capabilities {
  margin: 2rem 0;
}

.jmc-capabilities h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.capabilities-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.capability-section {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.capability-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.capability-section.automated {
  border-left: 4px solid #4caf50;
}

.capability-section.detailed {
  border-left: 4px solid #2196f3;
}

.capability-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.capability-icon {
  font-size: 2rem;
}

.capability-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.capability-content p {
  margin-bottom: 1.5rem;
  color: #666;
  line-height: 1.6;
}

.analysis-types,
.analysis-views {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.analysis-item,
.view-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.analysis-name,
.view-name {
  color: #333;
  font-weight: 600;
  font-size: 0.95rem;
}

.analysis-desc,
.view-desc {
  color: #666;
  font-size: 0.85rem;
  line-height: 1.5;
}

/* Solution Steps Styles */
.solution-steps {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.step-item {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.step-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content h7 {
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  display: block;
}

.step-details p {
  margin-bottom: 1rem;
  color: #666;
  line-height: 1.6;
}

.code-example {
  background: #2d3748;
  border-radius: 6px;
  padding: 1rem;
  margin: 0.5rem 0;
}

.code-example pre {
  margin: 0;
  color: #e2e8f0;
  font-size: 0.85rem;
  line-height: 1.4;
  overflow-x: auto;
}

.response-workflow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.5rem;
}

.workflow-step {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.workflow-arrow {
  color: #667eea;
  font-weight: bold;
}

.analysis-targets {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.target-item {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Best Practices Styles */
.best-practices {
  margin: 2rem 0;
}

.best-practices h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
}

.practices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.practice-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.practice-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.practice-card.production {
  border-left: 4px solid #4caf50;
}

.practice-card.analysis {
  border-left: 4px solid #2196f3;
}

.practice-card.team {
  border-left: 4px solid #ff9800;
}

.practice-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.practice-icon {
  font-size: 2rem;
}

.practice-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.practice-tips {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tip {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tip-label {
  color: #333;
  font-weight: 600;
  font-size: 0.95rem;
}

.tip-desc {
  color: #666;
  font-size: 0.85rem;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .sidebar {
    position: static;
    order: -1;
  }

  .outline-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .chapter-title {
    font-size: 2rem;
  }

  .chapter-subtitle {
    font-size: 1rem;
  }

  .container {
    padding: 0 1rem;
  }

  .analogy-comparison {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .comparison-arrow {
    transform: rotate(90deg);
    justify-self: center;
  }

  .pros-cons {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .principles-grid,
  .metrics-grid,
  .pitfalls-grid,
  .implications-grid,
  .chef-analogy,
  .locality-grid,
  .hypothesis-points,
  .optimizations-grid,
  .triggers-grid,
  .prevention-tips,
  .features-grid,
  .capabilities-showcase,
  .practices-grid {
    grid-template-columns: 1fr;
  }

  .law-explanation,
  .regions-visualization,
  .collectors-comparison,
  .tools-overview {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .gc-phases,
  .tier-flow {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .code-comparison {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .response-workflow,
  .analysis-targets {
    flex-direction: column;
    align-items: flex-start;
  }

  .workflow-arrow {
    transform: rotate(90deg);
  }

  .hierarchy-level {
    max-width: 100%;
  }

  .memory-arrow,
  .phase-arrow,
  .tier-arrow {
    transform: rotate(90deg);
    justify-self: center;
  }

  .vs-divider,
  .tool-connector {
    align-self: center;
    justify-self: center;
  }

  .smart-step {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .chapter-title {
    font-size: 1.5rem;
  }

  .performance-philosophy-showcase,
  .performance-terminology-showcase {
    padding: 1rem;
  }

  .concept-definition,
  .analogy-section,
  .real-world-section {
    padding: 1rem;
  }
}
</style>
